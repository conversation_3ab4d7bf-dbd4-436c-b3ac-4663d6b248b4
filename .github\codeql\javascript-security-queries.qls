# JavaScript Security Query Suite

# This query suite contains JavaScript-specific security queries
# It focuses on high-impact security vulnerabilities
# Updated for better Windows compatibility

- description: JavaScript Security Queries

# Use standard security queries from CodeQL
# Using only the security-and-quality suite to avoid conflicts and Windows path issues
# This provides a comprehensive set of security and quality checks
- uses: security-and-quality
