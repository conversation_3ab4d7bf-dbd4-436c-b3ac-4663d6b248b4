name: Python Tests (Reusable)

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      python-version:
        description: 'Python version to use'
        required: false
        default: '3.12'
        type: string
      test-path:
        description: 'Path to test files'
        required: false
        default: 'tests'
        type: string
  workflow_call:
    inputs:
      python-version:
        description: 'Python version to use'
        required: false
        default: '3.12'
        type: string
      test-path:
        description: 'Path to test files'
        required: false
        default: 'tests'
        type: string
  workflow_run:
    workflows: ["Gradual Lint Check"]
    types:
      - completed

jobs:
  test:
    if: ${{ github.event_name != 'workflow_run' || github.event.workflow_run.conclusion == 'success' }}
    runs-on: ubuntu-latest
    timeout-minutes: 45
    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ inputs.python-version || '3.12' }}

      - name: Cache uv dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache/uv
            ~/.uv
            .pytest_cache
          key: ${{ runner.os }}-uv-test-${{ hashFiles('**/requirements*.txt') }}
          restore-keys: |
            ${{ runner.os }}-uv-test-

      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH

      - name: Install dependencies
        run: |
          # Cleanup old distributions
          find . -type d -name "*.egg-info" -exec rm -rf {} + || true
          find . -type d -name "*.dist-info" -exec rm -rf {} + || true
          find . -name "*.egg" -exec rm -f {} + || true

          # Create virtual environment with uv
          uv venv .venv || {
            echo "Failed to create virtual environment with uv. Falling back to Python's venv module..."
            python -m venv .venv
          }

          # Activate virtual environment
          source .venv/bin/activate

          # Install dependencies with uv (including aiohttp and test dependencies)
          uv pip install aiohttp>=3.9.0 pytest pytest-cov pytest-xdist pytest-asyncio || {
            echo "Failed to install dependencies with uv pip. Installing/upgrading uv in the virtual environment and retrying..."
            python -m pip install --upgrade pip # Ensure pip is up-to-date for uv install
            python -m pip install --upgrade "uv>=0.7.8" # Ensure uv is available in venv
            uv pip install aiohttp>=3.9.0 pytest pytest-cov pytest-xdist pytest-asyncio
          }

          # Verify aiohttp is installed
          python -c "import aiohttp; print(f'aiohttp version: {aiohttp.__version__}')" || {
            echo "Failed to import aiohttp after uv install. This might indicate a deeper issue."
          }

          # Install project requirements
          if [ -f requirements-dev.txt ]; then
            uv pip install -r requirements-dev.txt || {
              echo "Failed to install from requirements-dev.txt with uv. Trying with pip..."
              python -m pip install -r requirements-dev.txt
            }
          fi

          if [ -f requirements.txt ]; then
            uv pip install -r requirements.txt || {
              echo "Failed to install from requirements.txt with uv. Trying with pip..."
              python -m pip install -r requirements.txt
            }
          fi

          # Install the package in development mode
          uv pip install -e . -v || {
            echo "Failed to install package with uv. Trying with pip..."
            python -m pip install -e . -v
          }

      - name: Create junit directory
        run: mkdir -p junit

      - name: Run tests with coverage
        env:
          PYTHONPATH: ${{ github.workspace }}
        run: |
          # Activate virtual environment
          source .venv/bin/activate

          # Run tests with pytest
          # Use the coverage threshold of 1% (consistent with other configurations)
          pytest ${{ inputs.test-path || 'tests' }} \
            -n auto \
            -v \
            --import-mode=importlib \
            --cov=. \
            --cov-report=xml \
            --cov-report=term-missing \
            --cov-fail-under=1 \
            --junitxml=junit/test-results.xml

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results-${{ github.run_id }}-${{ github.job }}
          path: junit/test-results.xml

      - name: Upload coverage report
        uses: actions/upload-artifact@v4
        with:
          name: coverage-report-${{ github.run_id }}
          path: coverage.xml
