name: "Unified CodeQL Configuration for All Platforms"

# This configuration file provides a unified configuration for CodeQL analysis
# across all platforms (Windows, macOS, Linux)
# Updated for better Windows compatibility

# Specify paths to analyze
paths:
  # JavaScript/TypeScript paths
  - 'ui'
  - 'sdk/javascript'
  - '**/*.js'
  - '**/*.jsx'
  - '**/*.ts'
  - '**/*.tsx'
  - '**/*.json'
  - '**/*.html'

  # Python paths
  - '**/*.py'
  - '**/*.pyw'
  - '**/*.pyi'

# Specify paths to exclude from analysis
paths-ignore:
  # Package management
  - '**/node_modules/**'
  # Don't ignore lock files for CodeQL analysis
  # - '**/package-lock.json'
  # - '**/yarn.lock'
  # - '**/pnpm-lock.yaml'
  - '**/.venv/**'
  - '**/venv/**'
  - '**/env/**'
  - '**/.env/**'
  - '**/pip-wheel-metadata/**'

  # Build artifacts
  - '**/dist/**'
  - '**/build/**'
  - '**/out/**'
  - '**/vendor/**'
  - '**/.next/**'
  - '**/__pycache__/**'
  - '**/*.egg-info/**'
  - '**/.pytest_cache/**'
  - '**/.mypy_cache/**'
  - '**/.ruff_cache/**'

  # Test files
  - '**/*.test.js'
  - '**/*.test.jsx'
  - '**/*.test.ts'
  - '**/*.test.tsx'
  - '**/*.spec.js'
  - '**/*.spec.jsx'
  - '**/*.spec.ts'
  - '**/*.spec.tsx'
  - '**/__tests__/**'
  - '**/__mocks__/**'
  - '**/test/**'
  - '**/tests/**'
  - '**/jest.config.js'
  - '**/jest.setup.js'
  - '**/cypress/**'
  - '**/playwright-report/**'
  - '**/pytest.ini'
  - '**/conftest.py'
  - '**/*.test.py'
  - '**/*_test.py'
  - '**/pytest_*.py'

  # Minified files and type definitions
  - '**/*.min.js'
  - '**/*.d.ts'

  # Configuration files
  - '**/.eslintrc.*'
  - '**/.prettierrc.*'
  - '**/tsconfig.json'
  - '**/babel.config.js'
  - '**/webpack.config.js'
  - '**/rollup.config.js'
  - '**/setup.py'
  - '**/setup.cfg'
  - '**/pyproject.toml'
  - '**/.flake8'
  - '**/.pylintrc'
  - '**/tox.ini'
  - '**/.coveragerc'
  - '**/.vscode/**'
  - '**/.idea/**'
  - '**/coverage/**'
  - '**/.git/**'

  # Documentation
  - '**/*.md'
  - '**/*.mdx'
  - '**/*.rst'
  - '**/docs/**'
  - '**/sphinx/**'

# Query filters
queries:
  # Use only the standard security-and-quality query suite to avoid conflicts and Windows path issues
  # This provides a comprehensive set of security and quality checks in a single suite
  - uses: security-and-quality

  # Include language-specific security queries
  - uses: ./javascript-security-queries.qls
  - uses: ./python-security-queries.qls

# Disable noisy alerts
disable-default-queries: false

# Query suite definitions
query-filters:
  - exclude:
      tags contain: test
  - exclude:
      tags contain: maintainability
      precision below: high
  - exclude:
      tags contain: correctness
      precision below: high

# Trap errors during extraction
trap-for-errors: true

# Database extraction settings
database:
  # Exclude files that are too large to analyze effectively
  max-file-size-mb: 25
  # Exclude files with too many lines
  max-lines-of-code: 50000
  # Exclude files with too many AST nodes
  max-ast-nodes: 1000000
  # Extraction timeout per file
  extraction-timeout: 300
  # Windows-specific settings
  windows:
    # Increase timeout for Windows
    extraction-timeout: 600
